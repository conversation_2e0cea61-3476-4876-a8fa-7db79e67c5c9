#!/usr/bin/env python
"""
Test script to verify support user functionality
"""
import os
import sys
import django
from django.db import connection

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from shop.models import Purchase
from shop.admin import get_user_is_support, set_user_is_support


def test_support_user_creation():
    """Test creating a support user"""
    print("=== Testing Support User Creation ===")
    
    # Create a test user
    username = "test_support"
    user, created = User.objects.get_or_create(
        username=username,
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'first_name': 'Test',
            'last_name': 'Support'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {username}")
    else:
        print(f"✅ Using existing test user: {username}")
    
    # Set support access
    set_user_is_support(user.id, True)
    print(f"✅ Set support access for user: {username}")
    
    # Verify support access
    is_support = get_user_is_support(user)
    print(f"✅ Support status verified: {is_support}")
    
    return user


def test_database_field():
    """Test the is_support field in database"""
    print("\n=== Testing Database Field ===")
    
    with connection.cursor() as cursor:
        # Check if field exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'auth_user' AND column_name = 'is_support'
        """)
        result = cursor.fetchone()
        
        if result:
            print("✅ is_support field exists in auth_user table")
        else:
            print("❌ is_support field NOT found in auth_user table")
            return False
        
        # Test setting and getting values
        cursor.execute("SELECT COUNT(*) FROM auth_user WHERE is_support = TRUE")
        support_count = cursor.fetchone()[0]
        print(f"✅ Found {support_count} support users in database")
        
    return True


def test_purchase_queryset_logic():
    """Test the purchase queryset logic for support users"""
    print("\n=== Testing Purchase Queryset Logic ===")
    
    # Get total purchase count
    total_purchases = Purchase.objects.count()
    print(f"📊 Total purchases in database: {total_purchases}")
    
    if total_purchases == 0:
        print("⚠️  No purchases found - creating test data would be needed for full testing")
        return True
    
    # Test search functionality
    sample_purchase = Purchase.objects.first()
    if sample_purchase and sample_purchase.ref_id:
        ref_id = sample_purchase.ref_id
        search_results = Purchase.objects.filter(ref_id__icontains=ref_id)
        print(f"✅ Search for ref_id '{ref_id}' returns {search_results.count()} results")
    
    return True


def test_admin_helper_functions():
    """Test the admin helper functions"""
    print("\n=== Testing Admin Helper Functions ===")
    
    # Test with non-existent user
    try:
        result = get_user_is_support(None)
        print(f"✅ get_user_is_support(None) returns: {result}")
    except Exception as e:
        print(f"❌ Error with None user: {e}")
        return False
    
    # Test with real user
    user = User.objects.first()
    if user:
        try:
            result = get_user_is_support(user)
            print(f"✅ get_user_is_support() works for user {user.username}: {result}")
        except Exception as e:
            print(f"❌ Error getting support status: {e}")
            return False
    
    return True


def display_admin_urls():
    """Display relevant admin URLs"""
    print("\n=== Admin URLs ===")
    print("🌐 Django Admin: http://127.0.0.1:8000/admin/")
    print("📋 Purchases: http://127.0.0.1:8000/admin/shop/purchase/")
    print("👥 Users: http://127.0.0.1:8000/admin/auth/user/")
    print("\n💡 Support User Instructions:")
    print("1. Login to admin with support credentials")
    print("2. Go to Purchases section")
    print("3. Use search box to search by reference ID")
    print("4. Without searching, no purchases will be shown")


def main():
    """Main test function"""
    print("Support User Functionality Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        test_database_field,
        test_admin_helper_functions,
        test_support_user_creation,
        test_purchase_queryset_logic,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} error: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        display_admin_urls()
        
        print(f"\n🔧 To create a support user, run:")
        print(f"python manage.py create_support_user <username> --email <email>")
    else:
        print("⚠️  Some tests failed - check the implementation")


if __name__ == "__main__":
    main()
