#!/usr/bin/env python
"""
Test script for the clean support user implementation
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.db import connection
from shop.models import Purchase


def set_user_support_status(user_id, is_support):
    """Helper to set support status"""
    with connection.cursor() as cursor:
        cursor.execute(
            "UPDATE auth_user SET is_support = %s WHERE id = %s",
            [is_support, user_id]
        )


def get_user_support_status(user_id):
    """Helper to get support status"""
    with connection.cursor() as cursor:
        cursor.execute(
            "SELECT is_support FROM auth_user WHERE id = %s",
            [user_id]
        )
        result = cursor.fetchone()
        return result[0] if result else False


def test_support_user_functionality():
    """Test the complete support user functionality"""
    print("=== Testing Clean Support User Implementation ===")
    
    # Create a test user
    test_user, created = User.objects.get_or_create(
        username='clean_support_test',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'first_name': 'Clean',
            'last_name': 'Test'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using existing test user: {test_user.username}")
    
    # Test 1: Set support status
    set_user_support_status(test_user.id, True)
    support_status = get_user_support_status(test_user.id)
    print(f"📊 Support status after setting: {support_status}")
    
    if support_status:
        print("✅ Successfully set user as support user")
    else:
        print("❌ Failed to set user as support user")
        return False
    
    # Test 2: Test admin access
    client = Client()
    login_success = client.login(username='clean_support_test', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test 3: Test purchase admin access
    response = client.get('/admin/shop/purchase/')
    print(f"Purchase admin access status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Can access purchase admin")
        
        # Check for support user message
        content = response.content.decode('utf-8')
        if 'exact reference ID' in content:
            print("✅ Support user message displayed")
        else:
            print("⚠️  Support user message not found")
        
    elif response.status_code == 403:
        print("⚠️  Access denied - user may need permissions assigned")
    else:
        print(f"❓ Unexpected status: {response.status_code}")
    
    # Test 4: Test with a purchase search
    purchase = Purchase.objects.first()
    if purchase and purchase.ref_id:
        print(f"\n--- Testing search with ref_id: {purchase.ref_id} ---")
        search_response = client.get(f'/admin/shop/purchase/?q={purchase.ref_id}')
        print(f"Search response status: {search_response.status_code}")
        
        if search_response.status_code == 200:
            search_content = search_response.content.decode('utf-8')
            if purchase.ref_id in search_content:
                print("✅ Purchase found in search results")
                
                # Check for mobile number and payment verified fields
                if 'mobile_number' in search_content or purchase.mobile_number in search_content:
                    print("✅ Mobile number field visible")
                else:
                    print("⚠️  Mobile number field not visible")
                
                if 'Payment Verified' in search_content:
                    print("✅ Payment Verified field visible")
                else:
                    print("⚠️  Payment Verified field not visible")
                    
            else:
                print("❌ Purchase not found in search results")
        else:
            print(f"❌ Search failed with status: {search_response.status_code}")
    else:
        print("⚠️  No purchase with ref_id found for testing")
    
    # Test 5: Remove support status
    set_user_support_status(test_user.id, False)
    non_support_status = get_user_support_status(test_user.id)
    print(f"\n📊 Support status after removing: {non_support_status}")
    
    if not non_support_status:
        print("✅ Successfully removed support status")
    else:
        print("❌ Failed to remove support status")
        return False
    
    return True


def main():
    """Main test function"""
    print("Clean Support User Implementation Test")
    print("=" * 50)
    
    success = test_support_user_functionality()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Clean implementation working:")
        print("- is_support field added to auth_user table")
        print("- Support users see enhanced list view with mobile number and payment verification")
        print("- Support users must search by exact ref_id")
        print("- Admin can edit support status via User admin")
        print("- No hardcoded permissions - admin controls access")
        print("\n📝 Usage:")
        print("1. Go to Django admin → Users")
        print("2. Edit any user")
        print("3. Check 'Is Support User' checkbox")
        print("4. Assign purchase permissions manually")
        print("5. Support user can access purchase admin with restrictions")
        print("\n🌐 Ready for use at: http://127.0.0.1:8000/admin/")
    else:
        print("\n❌ Some tests failed - check the implementation")


if __name__ == "__main__":
    main()
