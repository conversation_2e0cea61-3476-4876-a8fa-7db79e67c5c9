#!/usr/bin/env python
"""
Test script to verify list view filtering works correctly
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from shop.models import Purchase


def test_list_view_filtering():
    """Test that support users only see purchases when searching"""
    print("=== Testing List View Filtering ===")
    
    # Get total purchase count
    total_purchases = Purchase.objects.count()
    print(f"📊 Total purchases in database: {total_purchases}")
    
    if total_purchases == 0:
        print("❌ No purchases found for testing")
        return False
    
    # Create test client and login
    client = Client()
    login_success = client.login(username='test_support', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test 1: Access list view without search
    print("\n--- Test 1: List view without search ---")
    response = client.get('/admin/shop/purchase/')
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check if any purchase ref_ids are visible in the content
        purchases_with_ref = Purchase.objects.exclude(ref_id__isnull=True).exclude(ref_id='')[:5]
        visible_purchases = 0
        
        for purchase in purchases_with_ref:
            if purchase.ref_id and purchase.ref_id in content:
                visible_purchases += 1
                print(f"❌ Found purchase {purchase.ref_id} visible without search!")
        
        if visible_purchases == 0:
            print("✅ No purchases visible without search (correct)")
        else:
            print(f"❌ {visible_purchases} purchases visible without search (incorrect)")
            return False
    
    # Test 2: Access list view with search
    print("\n--- Test 2: List view with search ---")
    test_purchase = Purchase.objects.exclude(ref_id__isnull=True).exclude(ref_id='').first()
    
    if test_purchase:
        search_term = test_purchase.ref_id
        response = client.get(f'/admin/shop/purchase/?q={search_term}')
        print(f"Search response status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            if search_term in content:
                print(f"✅ Purchase {search_term} found in search results (correct)")
            else:
                print(f"❌ Purchase {search_term} NOT found in search results (incorrect)")
                return False
    
    # Test 3: Access individual purchase detail
    print("\n--- Test 3: Individual purchase detail ---")
    test_purchase = Purchase.objects.first()
    response = client.get(f'/admin/shop/purchase/{test_purchase.id}/change/')
    print(f"Detail view status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Can access individual purchase detail (correct)")
    else:
        print(f"❌ Cannot access individual purchase detail (incorrect)")
        return False
    
    return True


def main():
    """Main test function"""
    print("List View Filtering Test")
    print("=" * 50)
    
    success = test_list_view_filtering()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Support users:")
        print("- Cannot see purchases in list view without searching")
        print("- Can see purchases when searching by ref_id")
        print("- Can access individual purchase details")
        print("\n🌐 Ready for manual testing at: http://127.0.0.1:8000/admin/")
    else:
        print("\n❌ Some tests failed - check the implementation")


if __name__ == "__main__":
    main()
