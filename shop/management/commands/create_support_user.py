from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import connection
from getpass import getpass


class Command(BaseCommand):
    help = 'Create a user with support access'

    def add_arguments(self, parser):
        parser.add_argument('username', type=str, help='Username for the support user')
        parser.add_argument('--email', type=str, help='Email address for the user', default='')
        parser.add_argument('--first-name', type=str, help='First name of the user', default='')
        parser.add_argument('--last-name', type=str, help='Last name of the user', default='')
        parser.add_argument('--password', type=str, help='Password (if not provided, will prompt)')

    def handle(self, *args, **options):
        username = options['username']
        email = options.get('email', '')
        first_name = options.get('first_name', '')
        last_name = options.get('last_name', '')
        password = options.get('password')

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.ERROR(f'User "{username}" already exists')
            )
            
            # Ask if they want to make existing user a support user
            make_support = input(f'Make existing user "{username}" a support user? (y/n): ')
            if make_support.lower() == 'y':
                user = User.objects.get(username=username)
                self._set_support_access(user)
                return
            else:
                return

        # Get password if not provided
        if not password:
            password = getpass('Password: ')
            password_confirm = getpass('Password (again): ')

            if password != password_confirm:
                self.stdout.write(
                    self.style.ERROR('Passwords do not match')
                )
                return

            if len(password) < 8:
                self.stdout.write(
                    self.style.ERROR('Password must be at least 8 characters long')
                )
                return

        try:
            # Create the user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                is_staff=True  # Allow admin access
            )

            self._set_support_access(user)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating user: {e}')
            )

    def _set_support_access(self, user):
        """Set support access for the user"""
        try:
            # Set support access using database field
            with connection.cursor() as cursor:
                cursor.execute(
                    "UPDATE auth_user SET is_support = TRUE WHERE id = %s",
                    [user.id]
                )

            # Ensure user has staff access
            if not user.is_staff:
                user.is_staff = True
                user.save()

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created/updated support user "{user.username}" with ID {user.id}'
                )
            )
            self.stdout.write(
                self.style.WARNING(
                    f'Note: Admin must manually assign necessary permissions to this user in Django admin.'
                )
            )
            self.stdout.write(
                self.style.WARNING(
                    f'Recommended permissions: "shop | purchase | Can view purchase" and "shop | purchase | Can change purchase"'
                )
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f'User can now access Django admin at /admin/ with support-level restrictions'
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error setting support access: {e}')
            )
