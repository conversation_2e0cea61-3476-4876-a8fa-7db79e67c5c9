# Add is_support field to auth_user table

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0024_add_profit_sharing_system'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.RunSQL(
            "ALTER TABLE auth_user ADD COLUMN is_support BOOLEAN NOT NULL DEFAULT FALSE;",
            reverse_sql="ALTER TABLE auth_user DROP COLUMN is_support;"
        ),
    ]
