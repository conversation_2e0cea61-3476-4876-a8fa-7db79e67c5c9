from django.contrib import admin
from django.contrib import messages
from django.utils.html import format_html
from django.urls import reverse
from django_q.tasks import async_task
from .models import MinecraftServer, Category, ContentCreator, Item, Purchase, PurchaseItem, CommandJob, PlayerCountSnapshot, Platform, Launcher, Version
from .minecraft import is_user_present_on_server


@admin.register(MinecraftServer)
class MinecraftServerAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'ip', 'port', 'domain', 'rcon_port', 'api_port', 'enabled', 'published', 'proxy']
    list_filter = ['enabled', 'published', 'proxy']
    search_fields = ['name', 'display_name', 'ip', 'domain']
    list_editable = ['enabled', 'published']

    # Group fields for better organization
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'enabled', 'published')
        }),
        ('Connection Settings', {
            'fields': ('ip', 'port', 'domain', 'proxy')
        }),
        ('RCON Configuration', {
            'fields': ('rcon_port', 'rcon_password'),
            'description': 'RCON settings for remote command execution'
        }),
        ('API Configuration', {
            'fields': ('api_port', 'api_token'),
            'description': 'API settings for username validation'
        }),
    )


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'order', 'enabled', 'published']
    list_filter = ['enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['order', 'enabled', 'published']


@admin.register(ContentCreator)
class ContentCreatorAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'commission_rate', 'get_total_earned', 'get_total_unsettled', 'order', 'enabled', 'published']
    list_filter = ['enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['order', 'enabled', 'published']
    readonly_fields = ['get_total_earned', 'get_total_settled', 'get_total_unsettled']
    actions = ['settle_all_unsettled_purchases']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'description', 'image', 'enabled', 'published')
        }),
        ('Commission Settings', {
            'fields': ('commission_rate', 'admin_notes'),
            'description': 'Commission rate and internal notes'
        }),
        ('Earnings Summary', {
            'fields': ('get_total_earned', 'get_total_settled', 'get_total_unsettled'),
            'description': 'Read-only earnings information'
        }),
        ('Display Settings', {
            'fields': ('order',),
        }),
    )

    def get_total_earned(self, obj):
        return f"{obj.total_earned:,.2f}"
    get_total_earned.short_description = 'Total Earned'

    def get_total_settled(self, obj):
        return f"{obj.total_settled:,.2f}"
    get_total_settled.short_description = 'Total Settled'

    def get_total_unsettled(self, obj):
        return f"{obj.total_unsettled:,.2f}"
    get_total_unsettled.short_description = 'Total Unsettled'

    def settle_all_unsettled_purchases(self, request, queryset):
        """Admin action to settle all unsettled purchases for selected ContentCreators"""
        from django.utils import timezone

        total_settled = 0
        creators_processed = 0

        for creator in queryset:
            # Get all unsettled purchases for this creator
            unsettled_purchases = creator.referred_purchases.filter(
                referral_settlement_status='pending',
                referral_commission__isnull=False
            )

            count = unsettled_purchases.count()
            if count > 0:
                # Update all unsettled purchases to settled
                unsettled_purchases.update(
                    referral_settlement_status='settled',
                    referral_settled_at=timezone.now()
                )
                total_settled += count
                creators_processed += 1

        if total_settled > 0:
            self.message_user(
                request,
                f'Successfully settled {total_settled} purchase(s) for {creators_processed} ContentCreator(s).',
                messages.SUCCESS
            )
        else:
            self.message_user(
                request,
                'No unsettled purchases found for the selected ContentCreator(s).',
                messages.WARNING
            )

    settle_all_unsettled_purchases.short_description = "Settle all unsettled purchases for selected creators"


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'category', 'price', 'ucoin_price','expiration_days', 'minecraft_server', 'require_user_verification', 'order', 'enabled', 'published']
    list_filter = ['category','expiration_days', 'minecraft_server', 'require_user_verification', 'enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['order', 'require_user_verification', 'enabled', 'published']


class CommandJobInline(admin.TabularInline):
    model = CommandJob
    extra = 0
    readonly_fields = ['created_at', 'started_at', 'completed_at']
    fields = ['command_text', 'sequence_order', 'state', 'error_message', 'created_at', 'started_at', 'completed_at']
    ordering = ['sequence_order']


class PurchaseItemInline(admin.TabularInline):
    model = PurchaseItem
    extra = 0
    readonly_fields = ['created_at']
    fields = ['item', 'quantity', 'subscription_status', 'expires_at', 'created_at']


@admin.register(Purchase)
class PurchaseAdmin(admin.ModelAdmin):
    list_display = ['ref_id', 'get_items_display', 'minecraft_username', 'state', 'get_derived_state', 'created_at', 'payment_succeeded_at', 'get_referrer_info', 'get_commission_info']
    list_filter = ['state', 'referral_settlement_status', 'created_at', 'payment_succeeded_at', 'referrer']
    search_fields = ['ref_id', 'minecraft_username', 'mobile_number', 'zarinpal_ref_id']
    readonly_fields = ['ref_id', 'created_at', 'payment_succeeded_at', 'authority', 'zarinpal_ref_id', 'zarinpal_code', 'zarinpal_verify_response', 'referral_commission', 'referral_settled_at']
    date_hierarchy = 'created_at'
    inlines = [PurchaseItemInline]
    actions = ['retry_failed_commands', 'run_pending_orders', 'mark_referral_payments_settled', 'revert_referral_settlements', 'recalculate_referral_commissions']

    fieldsets = (
        ('Purchase Information', {
            'fields': ('ref_id', 'minecraft_username', 'mobile_number', 'state', 'created_at', 'payment_succeeded_at')
        }),
        ('Payment Information', {
            'fields': ('authority', 'zarinpal_ref_id', 'zarinpal_code', 'zarinpal_verify_response'),
            'description': 'Zarinpal payment gateway information'
        }),
        ('Referral Information', {
            'fields': ('referrer', 'referral_commission', 'referral_settlement_status', 'referral_settled_at'),
            'description': 'Commission and settlement information for referrals'
        }),
    )

    def get_items_display(self, obj):
        items = obj.purchase_items.all()
        if items.count() == 1:
            item = items.first()
            return f"{item.item.name} x{item.quantity}"
        else:
            return f"{items.count()} items"
    get_items_display.short_description = 'Items'

    def get_derived_state(self, obj):
        return obj.get_derived_state_from_commands()
    get_derived_state.short_description = 'Derived State'

    def get_referrer_info(self, obj):
        if obj.referrer:
            return f"{obj.referrer.name} ({obj.referrer.commission_rate}%)"
        return "-"
    get_referrer_info.short_description = 'Referrer'

    def get_commission_info(self, obj):
        if obj.referral_commission:
            status_display = obj.get_referral_settlement_status_display() if obj.referral_settlement_status else "N/A"
            return f"{obj.referral_commission:,.2f} ({status_display})"
        return "-"
    get_commission_info.short_description = 'Commission'

    def retry_failed_commands(self, request, queryset):
        """Admin action to retry failed commands for selected purchases"""
        retried_count = 0
        skipped_count = 0

        for purchase in queryset:
            # Check if purchase has failed commands
            failed_jobs = CommandJob.objects.filter(
                purchase_item__purchase=purchase,
                state=CommandJob.State.FAILED
            )

            if failed_jobs.exists():
                # Reset failed jobs to pending
                failed_jobs.update(
                    state=CommandJob.State.PENDING,
                    error_message=None,
                    started_at=None,
                    completed_at=None
                )

                # Queue purchase for re-execution
                async_task('shop.tasks.execute_purchase_commands', purchase.id)
                retried_count += 1
            else:
                skipped_count += 1

        if retried_count > 0:
            self.message_user(
                request,
                f'Successfully queued {retried_count} purchase(s) for command retry.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} purchase(s) with no failed commands.',
                messages.WARNING
            )

    retry_failed_commands.short_description = "Retry failed commands for selected purchases"

    def run_pending_orders(self, request, queryset):
        """Admin action to run pending orders for selected purchases with user verification"""
        executed_count = 0
        skipped_count = 0
        verification_failed_count = 0

        for purchase in queryset:
            # Get all purchase items with pending commands
            purchase_items_with_pending = []
            for purchase_item in purchase.purchase_items.all():
                pending_jobs = purchase_item.command_jobs.filter(state=CommandJob.State.PENDING)
                if pending_jobs.exists():
                    purchase_items_with_pending.append(purchase_item)

            if not purchase_items_with_pending:
                skipped_count += 1
                continue

            # Check user verification for items that require it
            can_execute = True
            for purchase_item in purchase_items_with_pending:
                item = purchase_item.item
                if item.require_user_verification and item.minecraft_server:
                    if not is_user_present_on_server(purchase.minecraft_username, item.minecraft_server):
                        can_execute = False
                        verification_failed_count += 1
                        break

            if can_execute:
                # Queue purchase for execution
                async_task('shop.tasks.execute_purchase_commands', purchase.id)
                executed_count += 1
            else:
                skipped_count += 1

        # Show results to admin
        if executed_count > 0:
            self.message_user(
                request,
                f'Successfully queued {executed_count} purchase(s) for execution.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} purchase(s) with no pending commands or failed user verification.',
                messages.WARNING
            )

        if verification_failed_count > 0:
            self.message_user(
                request,
                f'{verification_failed_count} purchase(s) failed user verification check.',
                messages.ERROR
            )

    run_pending_orders.short_description = "Run pending orders (with user verification)"

    def mark_referral_payments_settled(self, request, queryset):
        """Admin action to mark referral payments as settled"""
        from django.utils import timezone

        # Filter purchases that have pending referral commissions
        pending_purchases = queryset.filter(
            referral_settlement_status=Purchase.ReferralSettlementStatus.PENDING,
            referral_commission__isnull=False
        )

        settled_count = pending_purchases.count()

        if settled_count > 0:
            # Update settlement status and timestamp
            pending_purchases.update(
                referral_settlement_status=Purchase.ReferralSettlementStatus.SETTLED,
                referral_settled_at=timezone.now()
            )

            self.message_user(
                request,
                f'Successfully marked {settled_count} referral payment(s) as settled.',
                messages.SUCCESS
            )
        else:
            self.message_user(
                request,
                'No purchases with pending referral commissions found in the selection.',
                messages.WARNING
            )

    mark_referral_payments_settled.short_description = "Mark referral payments as settled"

    def revert_referral_settlements(self, request, queryset):
        """Admin action to revert referral settlements (unsettle purchases)"""
        # Filter purchases that have settled referral commissions
        settled_purchases = queryset.filter(
            referral_settlement_status='settled',
            referral_commission__isnull=False
        )

        reverted_count = settled_purchases.count()

        if reverted_count > 0:
            # Update settlement status back to pending and clear settlement timestamp
            settled_purchases.update(
                referral_settlement_status='pending',
                referral_settled_at=None
            )

            self.message_user(
                request,
                f'Successfully reverted {reverted_count} referral settlement(s) back to pending.',
                messages.SUCCESS
            )
        else:
            self.message_user(
                request,
                'No purchases with settled referral commissions found in the selection.',
                messages.WARNING
            )

    revert_referral_settlements.short_description = "Revert referral settlements (unsettle)"

    def recalculate_referral_commissions(self, request, queryset):
        """Admin action to recalculate referral commissions for selected purchases"""
        # Filter purchases that have referrers and successful payments
        eligible_purchases = queryset.filter(
            referrer__isnull=False,
            payment_succeeded_at__isnull=False
        )

        recalculated_count = 0
        skipped_count = 0

        for purchase in eligible_purchases:
            old_commission = purchase.referral_commission
            purchase.recalculate_referral_commission(force=True)
            purchase.save()

            if purchase.referral_commission != old_commission:
                recalculated_count += 1
            else:
                skipped_count += 1

        if recalculated_count > 0:
            self.message_user(
                request,
                f'Successfully recalculated {recalculated_count} referral commission(s).',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'{skipped_count} commission(s) remained unchanged.',
                messages.INFO
            )

        ineligible_count = queryset.exclude(
            referrer__isnull=False,
            payment_succeeded_at__isnull=False
        ).count()

        if ineligible_count > 0:
            self.message_user(
                request,
                f'Skipped {ineligible_count} purchase(s) without referrer or successful payment.',
                messages.WARNING
            )

    recalculate_referral_commissions.short_description = "Recalculate referral commissions"


@admin.register(PurchaseItem)
class PurchaseItemAdmin(admin.ModelAdmin):
    list_display = ['get_purchase_link', 'item', 'quantity', 'subscription_status', 'get_command_state', 'expires_at', 'created_at']
    list_filter = ['subscription_status', 'created_at', 'item']
    search_fields = ['purchase__ref_id', 'purchase__minecraft_username', 'item__name']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'
    inlines = [CommandJobInline]
    actions = ['retry_failed_commands', 'run_pending_orders']

    def get_purchase_link(self, obj):
        """Create a clickable link to the purchase admin page"""
        url = reverse('admin:shop_purchase_change', args=[obj.purchase.id])
        return format_html('<a href="{}">{}</a>', url, obj.purchase)
    get_purchase_link.short_description = 'Purchase'

    def get_command_state(self, obj):
        return obj.get_command_execution_state()
    get_command_state.short_description = 'Command State'

    def retry_failed_commands(self, request, queryset):
        """Admin action to retry failed commands for selected purchase items"""
        retried_count = 0
        skipped_count = 0

        for purchase_item in queryset:
            # Check if purchase item has failed commands
            failed_jobs = purchase_item.command_jobs.filter(state=CommandJob.State.FAILED)

            if failed_jobs.exists():
                # Reset failed jobs to pending
                failed_jobs.update(
                    state=CommandJob.State.PENDING,
                    error_message=None,
                    started_at=None,
                    completed_at=None
                )

                # Queue purchase item for re-execution
                async_task('shop.tasks.execute_purchase_item_commands', purchase_item.id)
                retried_count += 1
            else:
                skipped_count += 1

        if retried_count > 0:
            self.message_user(
                request,
                f'Successfully queued {retried_count} purchase item(s) for command retry.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} purchase item(s) with no failed commands.',
                messages.WARNING
            )

    retry_failed_commands.short_description = "Retry failed commands for selected purchase items"

    def run_pending_orders(self, request, queryset):
        """Admin action to run pending orders for selected purchase items with user verification"""
        executed_count = 0
        skipped_count = 0
        verification_failed_count = 0

        for purchase_item in queryset:
            # Check if purchase item has pending commands
            pending_jobs = purchase_item.command_jobs.filter(state=CommandJob.State.PENDING)
            if not pending_jobs.exists():
                skipped_count += 1
                continue

            # Check user verification if required
            item = purchase_item.item
            can_execute = True
            if item.require_user_verification and item.minecraft_server:
                username = purchase_item.purchase.minecraft_username
                if not is_user_present_on_server(username, item.minecraft_server):
                    can_execute = False
                    verification_failed_count += 1

            if can_execute:
                # Queue purchase item for execution
                async_task('shop.tasks.execute_purchase_item_commands', purchase_item.id)
                executed_count += 1
            else:
                skipped_count += 1

        # Show results to admin
        if executed_count > 0:
            self.message_user(
                request,
                f'Successfully queued {executed_count} purchase item(s) for execution.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} purchase item(s) with no pending commands or failed user verification.',
                messages.WARNING
            )

        if verification_failed_count > 0:
            self.message_user(
                request,
                f'{verification_failed_count} purchase item(s) failed user verification check.',
                messages.ERROR
            )

    run_pending_orders.short_description = "Run pending orders (with user verification)"


@admin.register(CommandJob)
class CommandJobAdmin(admin.ModelAdmin):
    list_display = ['id', 'get_purchase_info', 'get_item_name', 'get_command_preview', 'state', 'sequence_order', 'created_at', 'completed_at']
    list_filter = ['state', 'created_at', 'purchase_item__item']
    search_fields = ['purchase_item__purchase__ref_id', 'purchase_item__purchase__minecraft_username', 'command_text']
    readonly_fields = ['created_at', 'started_at', 'completed_at', 'django_q_task_id']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    actions = ['retry_failed_commands', 'run_pending_orders']

    def get_purchase_info(self, obj):
        """Create a clickable link to the purchase admin page"""
        url = reverse('admin:shop_purchase_change', args=[obj.purchase_item.purchase.id])
        return format_html('<a href="{}">{}</a>', url, obj.purchase_item.purchase)
    get_purchase_info.short_description = 'Purchase'

    def get_item_name(self, obj):
        return obj.purchase_item.item.name
    get_item_name.short_description = 'Item'

    def get_command_preview(self, obj):
        return obj.command_text[:50] + ('...' if len(obj.command_text) > 50 else '')
    get_command_preview.short_description = 'Command'

    def retry_failed_commands(self, request, queryset):
        """Admin action to retry selected failed command jobs"""
        # Calculate counts before making any changes
        failed_jobs = queryset.filter(state=CommandJob.State.FAILED)
        failed_jobs_count = failed_jobs.count()
        non_failed_jobs_count = queryset.exclude(state=CommandJob.State.FAILED).count()

        if failed_jobs_count > 0:
            # Get job IDs before updating
            failed_job_ids = list(failed_jobs.values_list('id', flat=True))

            # Reset failed jobs to pending
            failed_jobs.update(
                state=CommandJob.State.PENDING,
                error_message=None,
                started_at=None,
                completed_at=None
            )

            # Queue each job for individual execution
            for job_id in failed_job_ids:
                async_task('shop.tasks.execute_single_command', job_id)

            self.message_user(
                request,
                f'Successfully queued {failed_jobs_count} failed command job(s) for retry.',
                messages.SUCCESS
            )

        if non_failed_jobs_count > 0:
            self.message_user(
                request,
                f'Skipped {non_failed_jobs_count} command job(s) that are not in failed state.',
                messages.WARNING
            )

    retry_failed_commands.short_description = "Retry selected failed command jobs"

    def run_pending_orders(self, request, queryset):
        """Admin action to run pending command jobs with user verification"""
        executed_count = 0
        skipped_count = 0
        verification_failed_count = 0

        # Group command jobs by purchase item for efficient verification
        purchase_items_jobs = {}
        for job in queryset.filter(state=CommandJob.State.PENDING):
            purchase_item = job.purchase_item
            if purchase_item not in purchase_items_jobs:
                purchase_items_jobs[purchase_item] = []
            purchase_items_jobs[purchase_item].append(job)

        for purchase_item, jobs in purchase_items_jobs.items():
            # Check user verification if required
            item = purchase_item.item
            can_execute = True
            if item.require_user_verification and item.minecraft_server:
                username = purchase_item.purchase.minecraft_username
                if not is_user_present_on_server(username, item.minecraft_server):
                    can_execute = False
                    verification_failed_count += len(jobs)

            if can_execute:
                # Queue individual command jobs for execution
                for job in jobs:
                    async_task('shop.tasks.execute_single_command', job.id)
                    executed_count += 1
            else:
                skipped_count += len(jobs)

        # Count non-pending jobs that were selected
        non_pending_count = queryset.exclude(state=CommandJob.State.PENDING).count()

        # Show results to admin
        if executed_count > 0:
            self.message_user(
                request,
                f'Successfully queued {executed_count} pending command job(s) for execution.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} command job(s) due to failed user verification.',
                messages.WARNING
            )

        if verification_failed_count > 0:
            self.message_user(
                request,
                f'{verification_failed_count} command job(s) failed user verification check.',
                messages.ERROR
            )

        if non_pending_count > 0:
            self.message_user(
                request,
                f'Skipped {non_pending_count} command job(s) that are not in pending state.',
                messages.INFO
            )

    run_pending_orders.short_description = "Run pending command jobs (with user verification)"


class LauncherInline(admin.TabularInline):
    model = Launcher
    extra = 0
    fields = ['name', 'display_name', 'download_url', 'image_url', 'order', 'enabled', 'published']
    show_change_link = True


class VersionInline(admin.TabularInline):
    model = Version
    extra = 0
    fields = ['name', 'display_name', 'download_url', 'image_url', 'order', 'enabled', 'published']


@admin.register(Platform)
class PlatformAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'get_launchers_count', 'order', 'enabled', 'published', 'created_at']
    list_filter = ['enabled', 'published', 'created_at']
    search_fields = ['name', 'display_name']
    list_editable = ['order', 'enabled', 'published']
    inlines = [LauncherInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'enabled', 'published')
        }),
        ('Media & Download', {
            'fields': ('image_url', 'download_url', 'help'),
            'description': 'Help field supports Markdown formatting'
        }),
    )

    def get_launchers_count(self, obj):
        return obj.launchers.count()
    get_launchers_count.short_description = 'Launchers'


@admin.register(Launcher)
class LauncherAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'platform', 'get_versions_count', 'order', 'enabled', 'published', 'created_at']
    list_filter = ['platform', 'enabled', 'published', 'created_at']
    search_fields = ['name', 'display_name', 'platform__name']
    list_editable = ['order', 'enabled', 'published']
    inlines = [VersionInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'platform', 'enabled', 'published')
        }),
        ('Media & Download', {
            'fields': ('image_url', 'download_url', 'help'),
            'description': 'Help field supports Markdown formatting'
        }),
    )

    def get_versions_count(self, obj):
        return obj.versions.count()
    get_versions_count.short_description = 'Versions'


@admin.register(Version)
class VersionAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'get_launcher_info', 'order', 'enabled', 'published', 'created_at']
    list_filter = ['launcher__platform', 'launcher', 'enabled', 'published', 'created_at']
    search_fields = ['name', 'display_name', 'launcher__name', 'launcher__platform__name']
    list_editable = ['order', 'enabled', 'published']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'launcher', 'enabled', 'published')
        }),
        ('Media & Download', {
            'fields': ('image_url', 'download_url', 'help'),
            'description': 'Help field supports Markdown formatting'
        }),
    )

    def get_launcher_info(self, obj):
        return f"{obj.launcher.platform.name} - {obj.launcher.name}"
    get_launcher_info.short_description = 'Platform - Launcher'


@admin.register(PlayerCountSnapshot)
class PlayerCountSnapshotAdmin(admin.ModelAdmin):
    list_display = ['server', 'timestamp', 'online_players', 'max_players', 'query_successful']
    list_filter = ['server', 'query_successful', 'timestamp']
    search_fields = ['server__name', 'server__display_name']
    readonly_fields = ['timestamp', 'server', 'online_players', 'max_players', 'query_successful', 'error_message']
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']

    def has_add_permission(self, request):
        # Prevent manual creation of snapshots through admin
        return False

    def has_change_permission(self, request, obj=None):
        # Make snapshots read-only
        return False