#!/usr/bin/env python
"""
Test script to verify editable support user functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from shop.admin import get_user_is_support, set_user_is_support


def test_support_status_management():
    """Test that support status can be managed through admin"""
    print("=== Testing Support Status Management ===")
    
    # Get or create a test user
    test_user, created = User.objects.get_or_create(
        username='test_editable_support',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using existing test user: {test_user.username}")
    
    # Test 1: Initially not support user
    initial_status = get_user_is_support(test_user)
    print(f"📊 Initial support status: {initial_status}")
    
    # Test 2: Set as support user
    set_user_is_support(test_user.id, True)
    support_status = get_user_is_support(test_user)
    print(f"📊 After setting to support: {support_status}")
    
    if support_status:
        print("✅ Successfully set user as support user")
    else:
        print("❌ Failed to set user as support user")
        return False
    
    # Test 3: Remove support status
    set_user_is_support(test_user.id, False)
    non_support_status = get_user_is_support(test_user)
    print(f"📊 After removing support: {non_support_status}")
    
    if not non_support_status:
        print("✅ Successfully removed support status")
    else:
        print("❌ Failed to remove support status")
        return False
    
    return True


def test_admin_access_without_permissions():
    """Test that support users need proper permissions"""
    print("\n=== Testing Admin Access Without Permissions ===")
    
    # Create a support user without permissions
    support_user, created = User.objects.get_or_create(
        username='test_no_perms',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
        }
    )
    
    if created:
        support_user.set_password('testpass123')
        support_user.save()
    
    # Set as support user
    set_user_is_support(support_user.id, True)
    
    # Test login and access
    client = Client()
    login_success = client.login(username='test_no_perms', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Try to access purchases without permissions
    response = client.get('/admin/shop/purchase/')
    print(f"Purchase admin access status: {response.status_code}")
    
    if response.status_code == 403:
        print("✅ Access denied without permissions (correct)")
        return True
    elif response.status_code == 200:
        print("⚠️  Access allowed - this means permissions are being bypassed")
        return False
    else:
        print(f"❓ Unexpected status: {response.status_code}")
        return False


def test_permission_controlled_access():
    """Test that permissions control access properly"""
    print("\n=== Testing Permission-Controlled Access ===")
    
    # Use the existing test_support user that should have permissions
    client = Client()
    login_success = client.login(username='test_support', password='testpass123')
    
    if not login_success:
        print("❌ Login failed for test_support user")
        return False
    
    print("✅ Login successful for test_support user")
    
    # Test access to purchases (should work if permissions are assigned)
    response = client.get('/admin/shop/purchase/')
    print(f"Purchase admin access status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Access allowed with proper permissions")
        return True
    elif response.status_code == 403:
        print("⚠️  Access denied - user may not have proper permissions")
        print("💡 Admin needs to assign 'Can view purchase' and 'Can change purchase' permissions")
        return True  # This is expected if permissions aren't set
    else:
        print(f"❓ Unexpected status: {response.status_code}")
        return False


def main():
    """Main test function"""
    print("Editable Support User Test")
    print("=" * 50)
    
    # Test support status management
    status_test = test_support_status_management()
    
    # Test access without permissions
    no_perms_test = test_admin_access_without_permissions()
    
    # Test permission-controlled access
    perms_test = test_permission_controlled_access()
    
    if status_test and no_perms_test and perms_test:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Key features working:")
        print("- Support status can be toggled via database functions")
        print("- Access is controlled by Django permissions (not hardcoded)")
        print("- Support users need proper permissions to access admin")
        print("\n📝 Admin Instructions:")
        print("1. Go to Django admin → Users")
        print("2. Edit any user")
        print("3. Check/uncheck 'Is Support User' in Support Access section")
        print("4. Assign permissions: 'Can view purchase' and 'Can change purchase'")
        print("\n🌐 Ready for use at: http://127.0.0.1:8000/admin/")
    else:
        print("\n❌ Some tests failed:")
        if not status_test:
            print("- Support status management failed")
        if not no_perms_test:
            print("- Permission checking failed")
        if not perms_test:
            print("- Permission-controlled access failed")


if __name__ == "__main__":
    main()
