#!/usr/bin/env python
"""
Test script to verify support user can access purchase details
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import RequestFactory
from shop.models import Purchase
from shop.admin import PurchaseAdmin, get_user_is_support


def test_support_user_permissions():
    """Test that support user can access purchase details"""
    print("=== Testing Support User Purchase Access ===")
    
    # Get or create support user
    try:
        support_user = User.objects.get(username='test_support')
        print(f"✅ Found support user: {support_user.username}")
    except User.DoesNotExist:
        print("❌ Support user 'test_support' not found. Run test_support_user.py first.")
        return False
    
    # Verify user is support
    is_support = get_user_is_support(support_user)
    print(f"✅ Support status: {is_support}")
    
    if not is_support:
        print("❌ User is not marked as support user")
        return False
    
    # Get a sample purchase
    purchase = Purchase.objects.first()
    if not purchase:
        print("⚠️  No purchases found in database")
        return True
    
    print(f"📋 Testing with purchase ID: {purchase.id}, Ref: {purchase.ref_id}")
    
    # Create request factory
    factory = RequestFactory()
    
    # Test changelist permission
    request = factory.get('/admin/shop/purchase/')
    request.user = support_user
    
    admin = PurchaseAdmin(Purchase, None)
    
    # Test has_change_permission
    can_change = admin.has_change_permission(request, purchase)
    print(f"✅ Can access purchase details: {can_change}")
    
    # Test has_view_permission
    can_view = admin.has_view_permission(request, purchase)
    print(f"✅ Can view purchase: {can_view}")
    
    # Test queryset (should be empty without search)
    qs_no_search = admin.get_queryset(request)
    print(f"✅ Queryset without search: {qs_no_search.count()} items")
    
    # Test queryset with search
    request_with_search = factory.get(f'/admin/shop/purchase/?q={purchase.ref_id}')
    request_with_search.user = support_user
    qs_with_search = admin.get_queryset(request_with_search)
    print(f"✅ Queryset with search '{purchase.ref_id}': {qs_with_search.count()} items")
    
    return True


def main():
    """Main test function"""
    print("Support User Purchase Access Test")
    print("=" * 50)
    
    success = test_support_user_permissions()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("\n📝 Instructions for manual testing:")
        print("1. Go to http://127.0.0.1:8000/admin/")
        print("2. Login with support user credentials:")
        print("   Username: test_support")
        print("   Password: testpass123")
        print("3. Go to Purchases section")
        print("4. Search for a reference ID")
        print("5. Click on a purchase to view details")
        print("\n✅ The purchase detail page should now work correctly!")
    else:
        print("\n❌ Test failed - check the implementation")


if __name__ == "__main__":
    main()
