#!/usr/bin/env python
"""
Test script to verify purchase detail access works
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from shop.models import Purchase


def test_purchase_detail_access():
    """Test that support user can access purchase detail view"""
    print("=== Testing Purchase Detail Access ===")
    
    # Get a purchase to test with
    purchases = Purchase.objects.all()[:5]  # Get first 5 purchases
    
    if not purchases:
        print("❌ No purchases found in database")
        return False
    
    print(f"📋 Found {len(purchases)} purchases to test with")
    
    # Create test client and login
    client = Client()
    login_success = client.login(username='test_support', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test each purchase
    success_count = 0
    for purchase in purchases:
        print(f"\n--- Testing Purchase ID: {purchase.id} ---")
        print(f"Ref ID: {purchase.ref_id}")
        print(f"Username: {purchase.minecraft_username}")
        print(f"State: {purchase.state}")
        
        # Test direct access to purchase detail
        response = client.get(f'/admin/shop/purchase/{purchase.id}/change/')
        print(f"Detail view status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Can access purchase detail!")
            success_count += 1
            
            # Check if it's read-only (no save buttons)
            content = response.content.decode('utf-8')
            if 'name="_save"' not in content:
                print("✅ Form is read-only (no save button)")
            else:
                print("⚠️  Form has save button (should be read-only)")
                
        elif response.status_code == 302:
            print("❌ Redirected (likely permission denied)")
        elif response.status_code == 404:
            print("❌ Not found")
        else:
            print(f"❌ Unexpected status: {response.status_code}")
    
    print(f"\n=== Results ===")
    print(f"Successfully accessed: {success_count}/{len(purchases)} purchases")
    
    return success_count > 0


def test_search_and_access():
    """Test search functionality and then access"""
    print("\n=== Testing Search and Access Workflow ===")
    
    # Get a purchase with ref_id
    purchase = Purchase.objects.exclude(ref_id__isnull=True).exclude(ref_id='').first()
    
    if not purchase:
        print("❌ No purchase with ref_id found")
        return False
    
    print(f"Testing with Purchase ID: {purchase.id}, Ref: {purchase.ref_id}")
    
    # Create test client and login
    client = Client()
    login_success = client.login(username='test_support', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    # Test search
    search_response = client.get(f'/admin/shop/purchase/?q={purchase.ref_id}')
    print(f"Search response status: {search_response.status_code}")
    
    if search_response.status_code == 200:
        content = search_response.content.decode('utf-8')
        if purchase.ref_id in content:
            print("✅ Purchase found in search results")
        else:
            print("❌ Purchase not found in search results")
            return False
    
    # Test detail access after search
    detail_response = client.get(f'/admin/shop/purchase/{purchase.id}/change/')
    print(f"Detail access status: {detail_response.status_code}")
    
    if detail_response.status_code == 200:
        print("✅ SUCCESS: Can access purchase detail after search!")
        return True
    else:
        print(f"❌ FAILED: Cannot access detail. Status: {detail_response.status_code}")
        return False


def main():
    """Main test function"""
    print("Purchase Detail Access Test")
    print("=" * 50)
    
    # Test direct access
    direct_success = test_purchase_detail_access()
    
    # Test search workflow
    search_success = test_search_and_access()
    
    if direct_success and search_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Support users can now:")
        print("- Access purchase detail views")
        print("- View read-only purchase information")
        print("- Search and access purchases by ref_id")
        print("\n🌐 Ready for manual testing at: http://127.0.0.1:8000/admin/")
    else:
        print("\n❌ Some tests failed:")
        if not direct_success:
            print("- Direct purchase access failed")
        if not search_success:
            print("- Search workflow failed")


if __name__ == "__main__":
    main()
