#!/usr/bin/env python
"""
Test script to verify simplified support user functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from shop.models import Purchase
from shop.admin import get_user_is_support


def test_simplified_support_functionality():
    """Test the simplified support user functionality"""
    print("=== Testing Simplified Support Functionality ===")
    
    # Get a purchase to test with
    test_purchase = Purchase.objects.exclude(ref_id__isnull=True).exclude(ref_id='').first()
    
    if not test_purchase:
        print("❌ No purchase with ref_id found for testing")
        return False
    
    print(f"📋 Testing with Purchase: {test_purchase.ref_id}")
    print(f"   Mobile: {test_purchase.mobile_number}")
    print(f"   Zarinpal Code: {test_purchase.zarinpal_code}")
    
    # Create test client and login
    client = Client()
    login_success = client.login(username='test_support', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test 1: List view without search (should be empty)
    print("\n--- Test 1: List view without search ---")
    response = client.get('/admin/shop/purchase/')
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        if test_purchase.ref_id not in content:
            print("✅ No purchases visible without search (correct)")
        else:
            print("❌ Purchases visible without search (incorrect)")
            return False
    
    # Test 2: Exact search (should find the purchase)
    print("\n--- Test 2: Exact ref_id search ---")
    response = client.get(f'/admin/shop/purchase/?q={test_purchase.ref_id}')
    print(f"Search response status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check if purchase is found
        if test_purchase.ref_id in content:
            print(f"✅ Purchase {test_purchase.ref_id} found with exact search")
            
            # Check if mobile number is visible
            if test_purchase.mobile_number and test_purchase.mobile_number in content:
                print(f"✅ Mobile number {test_purchase.mobile_number} visible")
            else:
                print("⚠️  Mobile number not visible or empty")
            
            # Check payment verification status
            if test_purchase.zarinpal_code in [100, 101]:
                if '✓' in content or 'True' in content:  # Check for boolean true representation
                    print("✅ Payment verified status shown as true")
                else:
                    print("⚠️  Payment verified status not clearly shown as true")
            else:
                print(f"ℹ️  Payment not verified (code: {test_purchase.zarinpal_code})")
            
        else:
            print(f"❌ Purchase {test_purchase.ref_id} NOT found with exact search")
            return False
    
    # Test 3: Partial search (should NOT find the purchase)
    print("\n--- Test 3: Partial ref_id search ---")
    partial_ref = test_purchase.ref_id[:4]  # First 4 characters
    response = client.get(f'/admin/shop/purchase/?q={partial_ref}')
    print(f"Partial search response status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        if test_purchase.ref_id not in content:
            print(f"✅ Purchase NOT found with partial search '{partial_ref}' (correct)")
        else:
            print(f"❌ Purchase found with partial search '{partial_ref}' (incorrect)")
            return False
    
    # Test 4: Try to access change view (should be denied)
    print("\n--- Test 4: Change view access ---")
    response = client.get(f'/admin/shop/purchase/{test_purchase.id}/change/')
    print(f"Change view status: {response.status_code}")
    
    if response.status_code == 403 or response.status_code == 302:
        print("✅ Change view access denied (correct)")
    else:
        print(f"❌ Change view accessible (incorrect) - Status: {response.status_code}")
        return False
    
    # Test 5: Try to access User admin (should be denied)
    print("\n--- Test 5: User admin access ---")
    response = client.get('/admin/auth/user/')
    print(f"User admin status: {response.status_code}")
    
    if response.status_code == 403 or 'Users' not in response.content.decode('utf-8'):
        print("✅ User admin access denied (correct)")
    else:
        print("❌ User admin accessible (incorrect)")
        return False
    
    return True


def test_payment_verified_logic():
    """Test the payment verification logic"""
    print("\n=== Testing Payment Verification Logic ===")
    
    # Test different zarinpal codes
    test_cases = [
        (100, True, "First successful verification"),
        (101, True, "Already verified"),
        (200, False, "Failed verification"),
        (None, False, "No code"),
        (0, False, "Zero code")
    ]
    
    for code, expected, description in test_cases:
        # Create a mock purchase object
        class MockPurchase:
            def __init__(self, zarinpal_code):
                self.zarinpal_code = zarinpal_code
        
        mock_purchase = MockPurchase(code)
        
        # Test the logic from admin
        from shop.admin import PurchaseAdmin
        admin = PurchaseAdmin(Purchase, None)
        result = admin.get_payment_verified(mock_purchase)
        
        if result == expected:
            print(f"✅ Code {code} ({description}): {result} (correct)")
        else:
            print(f"❌ Code {code} ({description}): {result}, expected {expected}")
            return False
    
    return True


def main():
    """Main test function"""
    print("Simplified Support User Test")
    print("=" * 50)
    
    # Test payment verification logic
    payment_test = test_payment_verified_logic()
    
    # Test support functionality
    support_test = test_simplified_support_functionality()
    
    if payment_test and support_test:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Simplified support functionality working:")
        print("- Support users must use exact ref_id search")
        print("- Mobile number and payment verification visible in list")
        print("- No access to change view or user admin")
        print("- Only retry commands action available")
        print("\n🌐 Ready for use at: http://127.0.0.1:8000/admin/")
        print("\n📝 Note: Admin must manually assign permissions to support users")
    else:
        print("\n❌ Some tests failed:")
        if not payment_test:
            print("- Payment verification logic failed")
        if not support_test:
            print("- Support functionality failed")


if __name__ == "__main__":
    main()
